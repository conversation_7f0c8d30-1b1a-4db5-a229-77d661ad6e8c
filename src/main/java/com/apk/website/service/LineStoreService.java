package com.apk.website.service;

import com.apk.website.common.utils.PageUtils;
import com.apk.website.common.vo.PageData;
import com.apk.website.dto.linestore.LineStoreDetailReq;
import com.apk.website.dto.linestore.LineStoreListReq;
import com.apk.website.dto.linestore.LineStoreSearchReq;
import com.apk.website.entity.TEmojiData;
import com.apk.website.entity.TStickerData;
import com.apk.website.mapper.TEmojiInfoDataMapper;
import com.apk.website.mapper.TStickerInfoDataMapper;
import com.apk.website.vo.LineStoreEmojiDetailVo;
import com.apk.website.vo.LineStoreStickerDetailVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor

public class LineStoreService {

    private final TStickerInfoDataMapper stickerMapper;
    private final TEmojiInfoDataMapper emojiMapper;

    public <T> PageData<T> getStickerList(LineStoreListReq req) {
        if (req.getType() == 1) {
            LambdaQueryWrapper<TStickerData> wrapper = new LambdaQueryWrapper<>(TStickerData.class);
            wrapper.eq(TStickerData::getType, "0");
            wrapper.eq(req.getCategoryId() != null,TStickerData::getCategoryId, req.getCategoryId());
            wrapper.eq(req.getAuthorId() != null,TStickerData::getAuthorId, req.getAuthorId());

            IPage<TStickerData> page = new Page<>(req.getPageNum(), req.getPageSize());

            IPage<TStickerData> iPage = stickerMapper.selectPage(page, wrapper);

            return (PageData<T>) PageUtils.coverPage(iPage);
        }else if (req.getType() == 2){
            LambdaQueryWrapper<TEmojiData> wrapper = new LambdaQueryWrapper<>(TEmojiData.class);
            wrapper.eq(TEmojiData::getType, "0");
            wrapper.eq(req.getCategoryId() != null,TEmojiData::getCategoryId, req.getCategoryId());
            wrapper.eq(req.getAuthorId() != null,TEmojiData::getAuthorId, req.getAuthorId());

            IPage<TEmojiData> page = new Page<>(req.getPageNum(), req.getPageSize());

            IPage<TEmojiData> iPage = emojiMapper.selectPage(page, wrapper);

            return (PageData<T>) PageUtils.coverPage(iPage);
        }else {
            return null;
        }

    }
    public <T> List<T> search(LineStoreSearchReq req) {
        if (req.getType() == 1) {
            LambdaQueryWrapper<TStickerData> wrapper = new LambdaQueryWrapper<>(TStickerData.class);
            wrapper.like(TStickerData::getTitle, req.getKeywords());
            return (List<T>) stickerMapper.selectList(wrapper);
        }else if (req.getType() == 2){
            LambdaQueryWrapper<TEmojiData> wrapper = new LambdaQueryWrapper<>(TEmojiData.class);
            wrapper.like(TEmojiData::getTitle, req.getKeywords());
            return (List<T>) emojiMapper.selectList(wrapper);
        }else {
            return null;
        }

    }

    public <T> T detail(LineStoreDetailReq req) {
        if (req.getType() == 1) {
            LineStoreStickerDetailVo resp = new LineStoreStickerDetailVo();
            TStickerData result = stickerMapper.selectById(req.getId());

            resp.setId(Integer.parseInt(req.getId()));
            resp.setAuthorId(result.getAuthorId());
            resp.setAuthorName(result.getAuthorName());
            resp.setCategoryId(result.getCategoryId());
            resp.setCategoryName(result.getCategoryName());
            resp.setCover(result.getCover());
            resp.setDescription(result.getDescription());
            resp.setPrice(result.getPrice());
            resp.setStickersList(result.getStickersList());
            resp.setType(result.getType());
            resp.setCreateTime(result.getCreateTime());
            resp.setUpdateTime(result.getUpdateTime());

            // 获取
            //    private String prevId;
            //    private String nextId;
            //
            //    // 相同作者的其他作品
            //    private String otherWorks;
            //
            //    //相关作品
            //    private String relatedWorks;
            LambdaQueryWrapper<TStickerData> wrapper = new LambdaQueryWrapper<>(TStickerData.class);
            wrapper.eq(TStickerData::getAuthorId, result.getAuthorId());
            resp.setOtherStickerWorks(stickerMapper.selectList(wrapper));

            return (T) resp;
        }else if (req.getType() == 2){
            LineStoreEmojiDetailVo resp = new LineStoreEmojiDetailVo();
            TEmojiData result = emojiMapper.selectById(req.getId());

            // 添加空值检查
            if (result == null) {
                log.warn("表情包数据不存在, id: {}", req.getId());
                return null;
            }

            resp.setId(req.getId());
            resp.setAuthorId(result.getAuthorId());
            resp.setAuthorName(result.getAuthorName());
            resp.setCategoryId(result.getCategoryId());
            resp.setCategoryName(result.getCategoryName());
            resp.setCover(result.getCover());
            resp.setDescription(result.getDescription());
            resp.setPrice(result.getPrice());
            resp.setStickersList(result.getStickersList());
            resp.setType(result.getType());
            resp.setCreateTime(result.getCreateTime());
            resp.setUpdateTime(result.getUpdateTime());

            // 优化SQL查询：批量获取相关数据
            populateEmojiRelatedDataOptimized(resp, result);

            return (T) resp;
        }else {
            return null;
        }

    }

    /**
     * 优化的表情包相关数据获取方法
     * 减少数据库查询次数，提升性能
     */
    private void populateEmojiRelatedDataOptimized(LineStoreEmojiDetailVo resp, TEmojiData result) {
        String currentId = result.getId();
        Integer authorId = result.getAuthorId();
        Integer categoryId = result.getCategoryId();

        // 1. 获取相同作者的其他作品（排除当前记录）
        LambdaQueryWrapper<TEmojiData> authorWrapper = new LambdaQueryWrapper<>();
        authorWrapper.eq(TEmojiData::getAuthorId, authorId)
                    .ne(TEmojiData::getId, currentId)
                    .orderByDesc(TEmojiData::getCreateTime); // 按创建时间排序，更有意义
        resp.setOtherEmojiWorks(emojiMapper.selectList(authorWrapper));

        // 2. 优化随机相关作品获取 - 避免ORDER BY RAND()性能问题
        List<TEmojiData> relatedWorks = getOptimizedRandomEmojis(categoryId, currentId);
        resp.setRelatedEmojiWorks(relatedWorks);

        // 3. 批量获取上一条和下一条记录 - 使用一次查询获取
        setPrevAndNextEmojiOptimized(resp, currentId);
    }

    /**
     * 优化的随机相关作品获取
     * 使用自定义SQL方法提升性能
     */
    private List<TEmojiData> getOptimizedRandomEmojis(Integer categoryId, String excludeId) {
        // 使用自定义SQL获取总数，避免复杂的LambdaQueryWrapper
        Long totalCount = emojiMapper.countByCategoryExcludeId(categoryId, excludeId);
        if (totalCount == null || totalCount <= 0) {
            return null; // 返回空列表
        }

        int limit = 18; // 相关作品数量限制

        // 如果总数小于等于需要的数量，直接返回所有记录
        if (totalCount <= limit) {
            return emojiMapper.selectRandomRelatedEmojis(categoryId, excludeId, limit, 0);
        }

        // 使用随机偏移量替代ORDER BY RAND()，性能更好
        int randomOffset = (int) (Math.random() * (totalCount - limit));
        return emojiMapper.selectRandomRelatedEmojis(categoryId, excludeId, limit, randomOffset);
    }

    /**
     * 优化的上一条/下一条记录获取
     * 使用一次查询获取上一条和下一条，减少数据库交互
     */
    private void setPrevAndNextEmojiOptimized(LineStoreEmojiDetailVo resp, String currentId) {
        // 使用自定义SQL一次查询获取上一条和下一条
        List<TEmojiData> prevNextList = emojiMapper.selectPrevAndNext(currentId);

        String prevId = null;
        String nextId = null;

        // 解析结果（这里需要根据实际的nav_type字段来区分）
        for (TEmojiData item : prevNextList) {
            // 注意：由于MyBatis Plus的限制，这里仍然使用两次查询
            // 在实际项目中，可以考虑使用原生SQL或者MyBatis XML配置
            if (item.getId().compareTo(currentId) < 0) {
                if (prevId == null || item.getId().compareTo(prevId) > 0) {
                    prevId = item.getId();
                }
            } else if (item.getId().compareTo(currentId) > 0) {
                if (nextId == null || item.getId().compareTo(nextId) < 0) {
                    nextId = item.getId();
                }
            }
        }

        resp.setPrevId(prevId);
        resp.setNextId(nextId);
    }

}
