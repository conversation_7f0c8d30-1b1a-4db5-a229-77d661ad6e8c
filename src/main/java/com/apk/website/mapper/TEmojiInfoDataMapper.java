package com.apk.website.mapper;

import com.apk.website.entity.TEmojiData;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 表情包数据 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
public interface TEmojiInfoDataMapper extends BaseMapper<TEmojiData> {

    /**
     * 一次查询获取上一条和下一条记录
     * 使用UNION ALL优化性能
     */
    @Select("""
        SELECT * FROM (
            SELECT *, 'prev' as nav_type FROM t_emoji_data
            WHERE id < #{currentId}
            ORDER BY id DESC
            LIMIT 1
        ) prev_record
        UNION ALL
        SELECT * FROM (
            SELECT *, 'next' as nav_type FROM t_emoji_data
            WHERE id > #{currentId}
            ORDER BY id ASC
            LIMIT 1
        ) next_record
        """)
    List<TEmojiData> selectPrevAndNext(@Param("currentId") String currentId);

    /**
     * 获取同分类记录总数（用于随机偏移计算）
     */
    @Select("SELECT COUNT(*) FROM t_emoji_data WHERE category_id = #{categoryId} AND id != #{excludeId}")
    Long countByCategoryExcludeId(@Param("categoryId") Integer categoryId, @Param("excludeId") String excludeId);

    /**
     * 高效的随机相关作品获取
     * 使用更优化的随机算法
     */
    @Select("""
        SELECT * FROM t_emoji_data
        WHERE category_id = #{categoryId} AND id != #{excludeId}
        ORDER BY create_time DESC
        LIMIT #{limit} OFFSET #{offset}
        """)
    List<TEmojiData> selectRandomRelatedEmojis(@Param("categoryId") Integer categoryId,
                                               @Param("excludeId") String excludeId,
                                               @Param("limit") Integer limit,
                                               @Param("offset") Integer offset);
}
